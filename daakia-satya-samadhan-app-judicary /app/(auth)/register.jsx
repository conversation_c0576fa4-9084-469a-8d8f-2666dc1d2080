import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  Alert, 
  Dimensions, 
  Platform,
  KeyboardAvoidingView,
  TouchableOpacity,
  ActivityIndicator,
  Keyboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router, useNavigation } from 'expo-router';
import InputField from '../../components/Smalls/InputField';
import SelectField from '../../components/Smalls/SelectField';
import ImageUploader from '../../components/Larges/ImageUploader';
import { apiService } from '../../services/api';
import { validationService } from '../../services/validation';
import { AUTH_ERRORS } from '../../constants/auth';

const { width } = Dimensions.get('window');
const isTablet = width >= 768;
const isWeb = Platform.OS === 'web';

const responsiveSize = (size) => {
  if (isWeb) {
    return size * 0.8; 
  }
  if (isTablet) {
    return size * 1.2;
  }
  return size; 
};

const JUDGE_DESIGNATIONS = [
  'District Judge',
  'Additional District Judge',
  'Senior Civil Judge',
  'Civil Judge',
  'Metropolitan Magistrate',
  'Judicial Magistrate',
  'Chief Judicial Magistrate',
  'Additional Chief Judicial Magistrate'
];

const PROSECUTOR_DESIGNATIONS = [
  'Public Prosecutor',
  'Additional Public Prosecutor',
  'Assistant Public Prosecutor',
  'Special Public Prosecutor'
];

export default function SignUpScreen() {
  const [name, setName] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [designation, setDesignation] = useState('');
  const [court, setCourt] = useState('');
  const [aadhar, setAadhar] = useState('');
  const [emailId, setEmailId] = useState('');
  const [department, setDepartment] = useState('');
  const [role, setRole] = useState('');
  const [courts, setCourts] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingDepartments, setIsFetchingDepartments] = useState(false);
  const [uploadedUrl, setUploadedUrl] = useState(null);
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  const handleUploadComplete = (url) => {
    setUploadedUrl(url);
    console.log('Uploaded URL:', url);
  };

  const navigation = useNavigation();

  const handleLoginPress = () => {
    navigation.navigate('(auth)/login'); 
  };

  useEffect(() => {
    fetchCourts();
    
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const fetchCourts = async () => {
    try {
      const response = await apiService.fetchCourts();
      const courtOptions = response.data.map(court => ({
        key: court._id,
        value: court.name
      }));
      setCourts(courtOptions);
    } catch (error) {
      Alert.alert('Error', AUTH_ERRORS.FETCH_COURTS_FAILED);
    }
  };

  const fetchDepartments = async (courtId) => {
    if (!courtId) return;
    setIsFetchingDepartments(true);
    try {
      const response = await apiService.fetchCourtDepartments(courtId);
      const departmentOptions = response.data.map(dept => ({
        key: dept._id,
        value: dept.name
      }));
      setDepartments(departmentOptions);
    } catch (error) {
      Alert.alert('Error', AUTH_ERRORS.FETCH_DEPARTMENTS_FAILED);
    } finally {
      setIsFetchingDepartments(false);
    }
  };

  const handleSignUp = async () => {
    try {
      // Validate all inputs
      const registrationData = {
        name,
        mobileNumber,
        designation,
        court,
        aadhar,
        emailId,
        department: department || undefined, // Only include if selected
        displayUrl: uploadedUrl || undefined, // Only include if uploaded
        roles: [role], // Convert to array format
      };

      console.log('Registration Payload:', registrationData);

      validationService.validateRegistration(registrationData);

      setIsLoading(true);
      const response = await apiService.register(registrationData);
      
      console.log('Registration Response:', response);

      if (response.status === 'success') {
        Alert.alert('Success', 'Registration successful!', [
          {
            text: 'OK',
            onPress: () => router.replace('/(auth)/thankYouPage'),
          },
        ]);
      } else {
        throw new Error(response.message || AUTH_ERRORS.REGISTRATION_FAILED);
      }
    } catch (error) {
      console.error('Registration Error:', error);
      Alert.alert(
        'Error',
        error.message || 'An unexpected error occurred. Please try again later.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const getDesignationOptions = () => {
    if (role === 'judge') {
      return JUDGE_DESIGNATIONS.map(designation => ({
        key: designation,
        value: designation
      }));
    } else if (role === 'prosecutor') {
      return PROSECUTOR_DESIGNATIONS.map(designation => ({
        key: designation,
        value: designation
      }));
    }
    return [];
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style="dark" />
      <View style={styles.container}>
        <View style={[
          styles.headerContainer,
          keyboardVisible && { marginBottom: responsiveSize(10) }
        ]}>
          <Text style={[
            styles.header, 
            keyboardVisible && { fontSize: responsiveSize(22) }
          ]}>
            SIGN UP
          </Text>
          <Text style={styles.loginText}>
            Already have an account?{' '}
            <Text style={styles.loginLink} onPress={handleLoginPress}>
              LOG IN
            </Text>
          </Text>
        </View>
        
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.formContainer}>
            <InputField
              label="Name *"
              placeholder="Enter Full Name"
              value={name}
              onChangeText={setName}
            />

            <SelectField
              label="Role *"
              data={[
                { key: 'judge', value: 'Judge' },
                { key: 'prosecutor', value: 'Prosecutor' }
              ]}
              onSelect={(val) => {
                setRole(val);
                setDesignation(''); // Reset designation when role changes
              }}
              placeholder="Select Role"
              searchPlaceholder="Search Roles"
              padding={0}
            />

            <SelectField
              label="Court *"
              data={courts}
              onSelect={(val) => {
                setCourt(val);
                fetchDepartments(val);
              }}
              placeholder="Select Court"
              searchPlaceholder="Search Courts"
              padding={0}
            />

            <SelectField
              label="Department"
              data={departments}
              onSelect={(val) => setDepartment(val)}
              placeholder="Select Department"
              searchPlaceholder="Search Departments"
              disabled={!court}
              isLoading={isFetchingDepartments}
              padding={0}
            />

            <SelectField
              label="Designation *"
              data={getDesignationOptions()}
              onSelect={(val) => setDesignation(val)}
              placeholder="Select Designation"
              searchPlaceholder="Search Designations"
              disabled={!role}
              padding={0}
            />

            <KeyboardAvoidingView
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
              style={styles.keyboardAvoidingSection}
            >
              <InputField
                label="Aadhar No. *"
                placeholder="Enter Aadhar No."
                value={aadhar}
                onChangeText={setAadhar}
                keyboardType="numeric"
                maxLength={12}
              />

              <InputField
                label="Mobile No. *"
                placeholder="Enter Mobile No."
                value={mobileNumber}
                onChangeText={setMobileNumber}
                keyboardType="numeric"
                maxLength={10}
                prefix="+91"
              />

              <InputField
                label="Govt. Email ID *"
                placeholder="Enter Govt. Email ID"
                value={emailId}
                onChangeText={setEmailId}
                keyboardType="email-address"
              />

              <ImageUploader onUploadComplete={handleUploadComplete} />
            </KeyboardAvoidingView>

            <TouchableOpacity
              style={[styles.signUpButton, isLoading && styles.disabledButton]}
              onPress={handleSignUp}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.signUpButtonText}>Sign Up</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
  },
  headerContainer: {
    paddingHorizontal: responsiveSize(20),
    paddingTop: responsiveSize(10),
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: responsiveSize(20),
    paddingBottom: responsiveSize(20),
  },
  formContainer: {
    width: '100%',
  },
  header: {
    fontSize: responsiveSize(28),
    fontWeight: 'bold',
    marginBottom: responsiveSize(10),
    color: '#0B36A1',
    textAlign: 'center',
  },
  loginText: {
    fontSize: responsiveSize(16),
    marginBottom: responsiveSize(20),
    textAlign: 'center',
    color: '#c4c4c4c',
  },
  loginLink: {
    color: '#0647a1',
    fontWeight: 'bold',
  },
  keyboardAvoidingSection: {
    width: '100%',
  },
  signUpButton: {
    backgroundColor: '#0B36A1',
    padding: responsiveSize(15),
    borderRadius: 10,
    alignItems: 'center',
    marginTop: responsiveSize(20),
    width: isWeb ? '50%' : '100%',
    alignSelf: 'center',
  },
  disabledButton: {
    backgroundColor: '#c4c4c4c',
  },
  signUpButtonText: {
    color: 'white',
    fontSize: responsiveSize(18),
    fontWeight: 'bold',
  },
});